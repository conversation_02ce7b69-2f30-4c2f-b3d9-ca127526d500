interface NewsletterPaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  subscribersLoading: boolean;
}

export default function NewsletterPagination({
  currentPage,
  totalPages,
  onPageChange,
  subscribersLoading
}: NewsletterPaginationProps) {
  if (totalPages <= 1) return null;

  return (
    <div className="bg-white rounded-lg shadow p-4">
      <div className="flex items-center justify-between">
        <div className="text-sm text-gray-700">
          Trang {currentPage + 1} của {totalPages}
        </div>
        <div className="flex space-x-2">
          <button
            onClick={() => onPageChange(Math.max(currentPage - 1, 0))}
            disabled={currentPage === 0 || subscribersLoading}
            className="px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
          >
            Trước
          </button>
          
          {/* Page numbers */}
          {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
            const startPage = Math.max(0, Math.min(currentPage - 2, totalPages - 5));
            const pageNum = startPage + i;
            
            if (pageNum >= totalPages) return null;
            
            return (
              <button
                key={pageNum}
                onClick={() => onPageChange(pageNum)}
                disabled={subscribersLoading}
                className={`px-3 py-1 border rounded-md text-sm disabled:opacity-50 ${
                  currentPage === pageNum
                    ? 'bg-blue-600 text-white border-blue-600'
                    : 'border-gray-300 hover:bg-gray-50'
                }`}
              >
                {pageNum + 1}
              </button>
            );
          })}
          
          <button
            onClick={() => onPageChange(Math.min(currentPage + 1, totalPages - 1))}
            disabled={currentPage === totalPages - 1 || subscribersLoading}
            className="px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
          >
            Sau
          </button>
        </div>
      </div>
    </div>
  );
}