/* TipTap Editor Styles */
.ProseMirror {
  outline: none;
  min-height: 400px;
  padding: 1rem;
  font-family: inherit;
  line-height: 1.6;
}

.ProseMirror p {
  margin: 0.5rem 0;
}

.ProseMirror h1 {
  font-size: 1.875rem;
  font-weight: 700;
  margin: 1rem 0 0.5rem 0;
}

.ProseMirror h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0.75rem 0 0.5rem 0;
}

.ProseMirror h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0.5rem 0 0.25rem 0;
}

.ProseMirror ul,
.ProseMirror ol {
  margin: 0.5rem 0;
  padding-left: 1.5rem;
}

.ProseMirror blockquote {
  border-left: 4px solid #e5e7eb;
  padding-left: 1rem;
  margin: 1rem 0;
  font-style: italic;
  color: #6b7280;
}

.ProseMirror code {
  background-color: #f3f4f6;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
}

.ProseMirror pre {
  background-color: #1f2937;
  color: #f9fafb;
  padding: 1rem;
  border-radius: 0.5rem;
  overflow-x: auto;
  margin: 1rem 0;
}

.ProseMirror pre code {
  background-color: transparent;
  padding: 0;
  color: inherit;
}

.ProseMirror a {
  color: #3b82f6;
  text-decoration: underline;
}

.ProseMirror img {
  max-width: 100%;
  height: auto;
  border-radius: 0.5rem;
  margin: 1rem 0;
}

/* Dark mode styles */
.dark .ProseMirror {
  color: #f9fafb;
}

.dark .ProseMirror blockquote {
  border-left-color: #4b5563;
  color: #9ca3af;
}

.dark .ProseMirror code {
  background-color: #374151;
  color: #f9fafb;
}

.dark .ProseMirror pre {
  background-color: #111827;
}

/* Markdown Editor Styles */
.w-md-editor {
  border: none !important;
  border-radius: 0.5rem;
  overflow: hidden;
}

.w-md-editor-toolbar {
  background-color: #f9fafb !important;
  border-bottom: 1px solid #e5e7eb !important;
  padding: 0.5rem !important;
}

.dark .w-md-editor-toolbar {
  background-color: #374151 !important;
  border-bottom-color: #4b5563 !important;
}

.w-md-editor-toolbar-divider {
  background-color: #d1d5db !important;
}

.dark .w-md-editor-toolbar-divider {
  background-color: #6b7280 !important;
}

.w-md-editor-toolbar ul {
  background-color: #ffffff !important;
  border: 1px solid #e5e7eb !important;
  border-radius: 0.375rem !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1) !important;
}

.dark .w-md-editor-toolbar ul {
  background-color: #1f2937 !important;
  border-color: #4b5563 !important;
}

.w-md-editor-toolbar li {
  color: #374151 !important;
}

.dark .w-md-editor-toolbar li {
  color: #f9fafb !important;
}

.w-md-editor-toolbar li:hover {
  background-color: #f3f4f6 !important;
}

.dark .w-md-editor-toolbar li:hover {
  background-color: #374151 !important;
}

.w-md-editor-input {
  background-color: #ffffff !important;
  color: #374151 !important;
  font-family: 'Courier New', monospace !important;
  font-size: 14px !important;
  line-height: 1.6 !important;
}

.dark .w-md-editor-input {
  background-color: #1f2937 !important;
  color: #f9fafb !important;
}

.w-md-editor-preview {
  background-color: #ffffff !important;
  color: #374151 !important;
  font-family: inherit !important;
  line-height: 1.6 !important;
}

.dark .w-md-editor-preview {
  background-color: #1f2937 !important;
  color: #f9fafb !important;
}

.w-md-editor-preview h1,
.w-md-editor-preview h2,
.w-md-editor-preview h3,
.w-md-editor-preview h4,
.w-md-editor-preview h5,
.w-md-editor-preview h6 {
  font-weight: 600 !important;
  margin: 1rem 0 0.5rem 0 !important;
}

.w-md-editor-preview h1 {
  font-size: 1.875rem !important;
}

.w-md-editor-preview h2 {
  font-size: 1.5rem !important;
}

.w-md-editor-preview h3 {
  font-size: 1.25rem !important;
}

.w-md-editor-preview p {
  margin: 0.5rem 0 !important;
}

.w-md-editor-preview ul,
.w-md-editor-preview ol {
  margin: 0.5rem 0 !important;
  padding-left: 1.5rem !important;
}

.w-md-editor-preview blockquote {
  border-left: 4px solid #e5e7eb !important;
  padding-left: 1rem !important;
  margin: 1rem 0 !important;
  font-style: italic !important;
  color: #6b7280 !important;
}

.dark .w-md-editor-preview blockquote {
  border-left-color: #4b5563 !important;
  color: #9ca3af !important;
}

.w-md-editor-preview code {
  background-color: #f3f4f6 !important;
  padding: 0.125rem 0.25rem !important;
  border-radius: 0.25rem !important;
  font-family: 'Courier New', monospace !important;
  font-size: 0.875rem !important;
}

.dark .w-md-editor-preview code {
  background-color: #374151 !important;
  color: #f9fafb !important;
}

.w-md-editor-preview pre {
  background-color: #1f2937 !important;
  color: #f9fafb !important;
  padding: 1rem !important;
  border-radius: 0.5rem !important;
  overflow-x: auto !important;
  margin: 1rem 0 !important;
}

.w-md-editor-preview pre code {
  background-color: transparent !important;
  padding: 0 !important;
  color: inherit !important;
}

.w-md-editor-preview a {
  color: #3b82f6 !important;
  text-decoration: underline !important;
}

.w-md-editor-preview img {
  max-width: 100% !important;
  height: auto !important;
  border-radius: 0.5rem !important;
  margin: 1rem 0 !important;
} 