/* Page transition animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes pulse-soft {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

@keyframes bounce-soft {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-2px);
  }
}

/* Utility classes */
.animate-fadeInUp {
  animation: fadeInUp 0.3s ease-out;
}

.animate-fadeInDown {
  animation: fadeInDown 0.3s ease-out;
}

.animate-slideInRight {
  animation: slideInRight 0.3s ease-out;
}

.animate-slideInLeft {
  animation: slideInLeft 0.3s ease-out;
}

.animate-shimmer {
  animation: shimmer 2s infinite;
}

.animate-pulse-soft {
  animation: pulse-soft 2s infinite;
}

.animate-bounce-soft {
  animation: bounce-soft 1s infinite;
}

/* Hover effects */
.hover-lift {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.dark .hover-lift:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Stagger animations */
.stagger-item {
  opacity: 0;
  animation: fadeInUp 0.4s ease-out forwards;
}

.stagger-item:nth-child(1) { animation-delay: 0.1s; }
.stagger-item:nth-child(2) { animation-delay: 0.2s; }
.stagger-item:nth-child(3) { animation-delay: 0.3s; }
.stagger-item:nth-child(4) { animation-delay: 0.4s; }
.stagger-item:nth-child(5) { animation-delay: 0.5s; }
.stagger-item:nth-child(6) { animation-delay: 0.6s; }

/* Loading states */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

.dark .loading-skeleton {
  background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
  background-size: 200% 100%;
}

/* Sidebar specific animations */
.sidebar-item {
  transition: all 0.2s ease-out;
}

.sidebar-item:hover {
  transform: translateX(4px);
}

.sidebar-item.active {
  transform: translateX(4px);
}

/* Card animations */
.card-hover {
  transition: all 0.3s ease-out;
}

.card-hover:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Button animations */
.btn-pulse {
  animation: pulse-soft 2s infinite;
}

.btn-glow {
  animation: glow 2s ease-in-out infinite;
}

/* Notification animations */
.notification-badge {
  animation: bounce-soft 1s ease-in-out;
}

/* Progress animations */
.progress-bar {
  transition: width 0.3s ease-out;
}

/* Modal animations */
.modal-enter {
  animation: fadeInScale 0.3s ease-out;
}

.modal-exit {
  animation: fadeInScale 0.3s ease-out reverse;
}

/* Page transition animations */
.page-enter {
  animation: slideInFromRight 0.3s ease-out;
}

.page-exit {
  animation: slideInFromLeft 0.3s ease-out;
}

/* Responsive animations */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}